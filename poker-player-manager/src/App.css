/* App.css */
.app {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.app-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.app-main {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Add Player Form */
.add-player-form {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 2px solid #f0f0f0;
}

.add-player-form h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.form-group {
  display: flex;
  gap: 15px;
  align-items: center;
}

.player-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.player-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Buttons */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-add {
  background: #667eea;
  color: white;
  min-width: 120px;
}

.btn-add:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.btn-edit {
  background: #f39c12;
  color: white;
  padding: 8px 12px;
  font-size: 12px;
}

.btn-edit:hover {
  background: #e67e22;
}

.btn-remove {
  background: #e74c3c;
  color: white;
  padding: 8px 12px;
  font-size: 12px;
}

.btn-remove:hover {
  background: #c0392b;
}

.btn-save {
  background: #27ae60;
  color: white;
  padding: 6px 10px;
  font-size: 12px;
}

.btn-save:hover {
  background: #229954;
}

.btn-cancel {
  background: #95a5a6;
  color: white;
  padding: 6px 10px;
  font-size: 12px;
}

.btn-cancel:hover {
  background: #7f8c8d;
}

/* Player List */
.player-list h2 {
  color: #333;
  margin-bottom: 25px;
  font-size: 1.5rem;
}

.player-list-empty {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-style: italic;
}

.player-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

/* Player Item */
.player-item {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.player-item:hover {
  border-color: #667eea;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.player-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.player-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.player-actions {
  display: flex;
  gap: 8px;
}

/* Player Edit */
.player-edit {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.player-edit-input {
  padding: 10px 12px;
  border: 2px solid #667eea;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
}

.player-edit-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.player-edit-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 15px;
  }

  .app-main {
    padding: 20px;
  }

  .form-group {
    flex-direction: column;
    align-items: stretch;
  }

  .player-grid {
    grid-template-columns: 1fr;
  }

  .app-header h1 {
    font-size: 2rem;
  }
}
