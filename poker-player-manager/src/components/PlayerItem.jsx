import { useState } from 'react'
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  TextField,
  Tooltip,
  Avatar
} from '@mui/material'
import {
  Edit,
  Delete,
  Check,
  Close,
  Person
} from '@mui/icons-material'

function PlayerItem({ player, onRemove, onRename }) {
  const [isEditing, setIsEditing] = useState(false)
  const [editName, setEditName] = useState(player.name)

  const handleSave = () => {
    if (editName.trim() && editName !== player.name) {
      onRename(editName)
    }
    setIsEditing(false)
    setEditName(player.name)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditName(player.name)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  return (
    <Card
      elevation={3}
      sx={{
        height: '100%',
        borderRadius: 2,
        border: '1px solid',
        borderColor: 'divider',
        '&:hover': {
          borderColor: 'primary.main',
        },
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {isEditing ? (
          <Box>
            <TextField
              fullWidth
              variant="outlined"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onKeyDown={handleKeyPress}
              onBlur={handleSave}
              autoFocus
              size="small"
              sx={{ mb: 2 }}
            />
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Tooltip title="Save changes">
                <IconButton
                  onClick={handleSave}
                  color="success"
                  size="small"
                >
                  <Check />
                </IconButton>
              </Tooltip>
              <Tooltip title="Cancel">
                <IconButton
                  onClick={handleCancel}
                  color="error"
                  size="small"
                >
                  <Close />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        ) : (
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  mr: 2,
                  width: 40,
                  height: 40,
                }}
              >
                <Person />
              </Avatar>
              <Typography
                variant="h6"
                component="div"
                sx={{
                  flexGrow: 1,
                  fontWeight: 600,
                  color: 'text.primary',
                }}
              >
                {player.name}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Tooltip title="Rename player">
                <IconButton
                  onClick={() => setIsEditing(true)}
                  color="primary"
                  size="small"
                >
                  <Edit />
                </IconButton>
              </Tooltip>
              <Tooltip title="Remove player">
                <IconButton
                  onClick={onRemove}
                  color="error"
                  size="small"
                >
                  <Delete />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

export default PlayerItem
