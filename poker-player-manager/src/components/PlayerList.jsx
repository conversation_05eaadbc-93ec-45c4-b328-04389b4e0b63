import { Box, Typography, Grid, Paper } from '@mui/material'
import { Groups } from '@mui/icons-material'
import PlayerItem from './PlayerItem'

function PlayerList({ players, onRemovePlayer, onRenamePlayer }) {
  if (players.length === 0) {
    return (
      <Paper
        elevation={1}
        sx={{
          p: 6,
          textAlign: 'center',
          backgroundColor: '#f8f9fa',
          borderRadius: 2,
        }}
      >
        <Groups sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" sx={{ fontStyle: 'italic' }}>
          No players added yet. Add your first player above!
        </Typography>
      </Paper>
    )
  }

  return (
    <Box>
      <Typography
        variant="h4"
        component="h2"
        gutterBottom
        sx={{
          mb: 3,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
        }}
      >
        <Groups />
        Players ({players.length})
      </Typography>
      <Grid container spacing={3}>
        {players.map(player => (
          <Grid item xs={12} sm={6} md={4} key={player.id}>
            <PlayerItem
              player={player}
              onRemove={() => onRemovePlayer(player.id)}
              onRename={(newName) => onRenamePlayer(player.id, newName)}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  )
}

export default PlayerList
