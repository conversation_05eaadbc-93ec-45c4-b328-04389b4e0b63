import { useState } from 'react'

function AddPlayerForm({ onAddPlayer }) {
  const [playerName, setPlayerName] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    if (playerName.trim()) {
      onAddPlayer(playerName)
      setPlayerName('')
    }
  }

  return (
    <div className="add-player-form">
      <h2>Add New Player</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <input
            type="text"
            value={playerName}
            onChange={(e) => setPlayerName(e.target.value)}
            placeholder="Enter player name..."
            className="player-input"
            maxLength={50}
          />
          <button type="submit" className="btn btn-add">
            Add Player
          </button>
        </div>
      </form>
    </div>
  )
}

export default AddPlayerForm
