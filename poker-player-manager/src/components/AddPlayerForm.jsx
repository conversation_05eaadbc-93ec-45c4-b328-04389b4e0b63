import { useState } from 'react'
import { Box, TextField, Button, Typography, Paper } from '@mui/material'
import { PersonAdd } from '@mui/icons-material'

function AddPlayerForm({ onAddPlayer }) {
  const [playerName, setPlayerName] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    if (playerName.trim()) {
      onAddPlayer(playerName)
      setPlayerName('')
    }
  }

  return (
    <Paper
      elevation={2}
      sx={{
        p: 3,
        mb: 4,
        borderRadius: 2,
        background: 'linear-gradient(45deg, #f8f9fa 0%, #ffffff 100%)',
      }}
    >
      <Typography variant="h4" component="h2" gutterBottom sx={{ mb: 3 }}>
        Add New Player
      </Typography>
      <Box component="form" onSubmit={handleSubmit}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
          <TextField
            fullWidth
            variant="outlined"
            value={playerName}
            onChange={(e) => setPlayerName(e.target.value)}
            placeholder="Enter player name..."
            inputProps={{ maxLength: 50 }}
            sx={{
              '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                  borderColor: 'primary.main',
                },
              },
            }}
          />
          <Button
            type="submit"
            variant="contained"
            size="large"
            startIcon={<PersonAdd />}
            sx={{
              minWidth: 140,
              height: 56,
              borderRadius: 2,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 600,
            }}
          >
            Add Player
          </Button>
        </Box>
      </Box>
    </Paper>
  )
}

export default AddPlayerForm
