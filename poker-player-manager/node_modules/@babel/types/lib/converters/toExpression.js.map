{"version": 3, "names": ["_index", "require", "_default", "exports", "default", "toExpression", "node", "isExpressionStatement", "expression", "isExpression", "isClass", "type", "abstract", "isFunction", "Error"], "sources": ["../../src/converters/toExpression.ts"], "sourcesContent": ["import {\n  isExpression,\n  isFunction,\n  isClass,\n  isExpressionStatement,\n} from \"../validators/generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default toExpression as {\n  (node: t.Function): t.FunctionExpression;\n  (node: t.Class): t.ClassExpression;\n  (\n    node: t.ExpressionStatement | t.Expression | t.Class | t.Function,\n  ): t.Expression;\n};\n\nfunction toExpression(\n  node: t.ExpressionStatement | t.Expression | t.Class | t.Function,\n): t.Expression {\n  if (isExpressionStatement(node)) {\n    node = node.expression;\n  }\n\n  // return unmodified node\n  // important for things like ArrowFunctions where\n  // type change from ArrowFunction to FunctionExpression\n  // produces bugs like -> `()=>a` to `function () a`\n  // without generating a BlockStatement for it\n  // ref: https://github.com/babel/babili/issues/130\n  if (isExpression(node)) {\n    return node;\n  }\n\n  // convert all classes and functions\n  // ClassDeclaration -> ClassExpression\n  // FunctionDeclaration, ObjectMethod, ClassMethod -> FunctionExpression\n  if (isClass(node)) {\n    // @ts-expect-error todo(flow->ts): avoid type unsafe mutations\n    node.type = \"ClassExpression\";\n    // abstract modifiers are only allowed on class declarations\n    node.abstract = false;\n  } else if (isFunction(node)) {\n    // @ts-expect-error todo(flow->ts): avoid type unsafe mutations\n    node.type = \"FunctionExpression\";\n  }\n\n  // if it's still not an expression\n  if (!isExpression(node)) {\n    throw new Error(`cannot turn ${node.type} to an expression`);\n  }\n\n  return node;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAK0C,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAG3BC,YAAY;AAQ3B,SAASA,YAAYA,CACnBC,IAAiE,EACnD;EACd,IAAI,IAAAC,4BAAqB,EAACD,IAAI,CAAC,EAAE;IAC/BA,IAAI,GAAGA,IAAI,CAACE,UAAU;EACxB;EAQA,IAAI,IAAAC,mBAAY,EAACH,IAAI,CAAC,EAAE;IACtB,OAAOA,IAAI;EACb;EAKA,IAAI,IAAAI,cAAO,EAACJ,IAAI,CAAC,EAAE;IAEjBA,IAAI,CAACK,IAAI,GAAG,iBAAiB;IAE7BL,IAAI,CAACM,QAAQ,GAAG,KAAK;EACvB,CAAC,MAAM,IAAI,IAAAC,iBAAU,EAACP,IAAI,CAAC,EAAE;IAE3BA,IAAI,CAACK,IAAI,GAAG,oBAAoB;EAClC;EAGA,IAAI,CAAC,IAAAF,mBAAY,EAACH,IAAI,CAAC,EAAE;IACvB,MAAM,IAAIQ,KAAK,CAAC,eAAeR,IAAI,CAACK,IAAI,mBAAmB,CAAC;EAC9D;EAEA,OAAOL,IAAI;AACb", "ignoreList": []}