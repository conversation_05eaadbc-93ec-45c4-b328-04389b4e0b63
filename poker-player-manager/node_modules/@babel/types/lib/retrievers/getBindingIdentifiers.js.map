{"version": 3, "names": ["_index", "require", "getBindingIdentifiers", "node", "duplicates", "outerOnly", "newBindingsOnly", "search", "concat", "ids", "Object", "create", "length", "id", "shift", "isAssignmentExpression", "isUnaryExpression", "isUpdateExpression", "isIdentifier", "_ids", "name", "push", "isExportDeclaration", "isExportAllDeclaration", "isDeclaration", "declaration", "isFunctionDeclaration", "isFunctionExpression", "keys", "type", "i", "key", "nodes", "Array", "isArray", "DeclareClass", "DeclareFunction", "DeclareModule", "DeclareVariable", "DeclareInterface", "DeclareTypeAlias", "DeclareOpaqueType", "InterfaceDeclaration", "TypeAlias", "OpaqueType", "CatchClause", "LabeledStatement", "UnaryExpression", "AssignmentExpression", "ImportSpecifier", "ImportNamespaceSpecifier", "ImportDefaultSpecifier", "ImportDeclaration", "TSImportEqualsDeclaration", "ExportSpecifier", "ExportNamespaceSpecifier", "ExportDefaultSpecifier", "FunctionDeclaration", "FunctionExpression", "ArrowFunctionExpression", "ObjectMethod", "ClassMethod", "ClassPrivateMethod", "ForInStatement", "ForOfStatement", "ClassDeclaration", "ClassExpression", "RestElement", "UpdateExpression", "ObjectProperty", "AssignmentPattern", "ArrayPattern", "ObjectPattern", "VariableDeclaration", "VariableDeclarator"], "sources": ["../../src/retrievers/getBindingIdentifiers.ts"], "sourcesContent": ["import {\n  isExportDeclaration,\n  isIdentifier,\n  isClassExpression,\n  isDeclaration,\n  isFunctionDeclaration,\n  isFunctionExpression,\n  isExportAllDeclaration,\n  isAssignmentExpression,\n  isUnaryExpression,\n  isUpdateExpression,\n} from \"../validators/generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport { getBindingIdentifiers as default };\n\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates: true,\n  outerOnly?: boolean,\n  newBindingsOnly?: boolean,\n): Record<string, Array<t.Identifier>>;\n\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates?: false,\n  outerOnly?: boolean,\n  newBindingsOnly?: boolean,\n): Record<string, t.Identifier>;\n\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates?: boolean,\n  outerOnly?: boolean,\n  newBindingsOnly?: boolean,\n): Record<string, t.Identifier> | Record<string, Array<t.Identifier>>;\n\n/**\n * Return a list of binding identifiers associated with the input `node`.\n */\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates?: boolean,\n  outerOnly?: boolean,\n  newBindingsOnly?: boolean,\n): Record<string, t.Identifier> | Record<string, Array<t.Identifier>> {\n  const search: t.Node[] = [].concat(node);\n  const ids = Object.create(null);\n\n  while (search.length) {\n    const id = search.shift();\n    if (!id) continue;\n\n    if (\n      newBindingsOnly &&\n      // These nodes do not introduce _new_ bindings, but they are included\n      // in getBindingIdentifiers.keys for backwards compatibility.\n      // TODO(@nicolo-ribaudo): Check if we can remove them from .keys in a\n      // backward-compatible way, and if not what we need to do to remove them\n      // in Babel 8.\n      (isAssignmentExpression(id) ||\n        isUnaryExpression(id) ||\n        isUpdateExpression(id))\n    ) {\n      continue;\n    }\n\n    if (isIdentifier(id)) {\n      if (duplicates) {\n        const _ids = (ids[id.name] = ids[id.name] || []);\n        _ids.push(id);\n      } else {\n        ids[id.name] = id;\n      }\n      continue;\n    }\n\n    if (isExportDeclaration(id) && !isExportAllDeclaration(id)) {\n      if (isDeclaration(id.declaration)) {\n        search.push(id.declaration);\n      }\n      continue;\n    }\n\n    if (outerOnly) {\n      if (isFunctionDeclaration(id)) {\n        search.push(id.id);\n        continue;\n      }\n\n      if (\n        isFunctionExpression(id) ||\n        (process.env.BABEL_8_BREAKING && isClassExpression(id))\n      ) {\n        continue;\n      }\n    }\n\n    const keys = getBindingIdentifiers.keys[id.type];\n\n    if (keys) {\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const nodes =\n          // @ts-expect-error key must present in id\n          id[key] as t.Node[] | t.Node | undefined | null;\n        if (nodes) {\n          if (Array.isArray(nodes)) {\n            search.push(...nodes);\n          } else {\n            search.push(nodes);\n          }\n        }\n      }\n    }\n  }\n  return ids;\n}\n\n/**\n * Mapping of types to their identifier keys.\n */\ntype KeysMap = {\n  [N in t.Node as N[\"type\"]]?: (keyof N)[];\n};\n\nconst keys: KeysMap = {\n  DeclareClass: [\"id\"],\n  DeclareFunction: [\"id\"],\n  DeclareModule: [\"id\"],\n  DeclareVariable: [\"id\"],\n  DeclareInterface: [\"id\"],\n  DeclareTypeAlias: [\"id\"],\n  DeclareOpaqueType: [\"id\"],\n  InterfaceDeclaration: [\"id\"],\n  TypeAlias: [\"id\"],\n  OpaqueType: [\"id\"],\n\n  CatchClause: [\"param\"],\n  LabeledStatement: [\"label\"],\n  UnaryExpression: [\"argument\"],\n  AssignmentExpression: [\"left\"],\n\n  ImportSpecifier: [\"local\"],\n  ImportNamespaceSpecifier: [\"local\"],\n  ImportDefaultSpecifier: [\"local\"],\n  ImportDeclaration: [\"specifiers\"],\n  TSImportEqualsDeclaration: [\"id\"],\n\n  ExportSpecifier: [\"exported\"],\n  ExportNamespaceSpecifier: [\"exported\"],\n  ExportDefaultSpecifier: [\"exported\"],\n\n  FunctionDeclaration: [\"id\", \"params\"],\n  FunctionExpression: [\"id\", \"params\"],\n  ArrowFunctionExpression: [\"params\"],\n  ObjectMethod: [\"params\"],\n  ClassMethod: [\"params\"],\n  ClassPrivateMethod: [\"params\"],\n\n  ForInStatement: [\"left\"],\n  ForOfStatement: [\"left\"],\n\n  ClassDeclaration: [\"id\"],\n  ClassExpression: [\"id\"],\n\n  RestElement: [\"argument\"],\n  UpdateExpression: [\"argument\"],\n\n  ObjectProperty: [\"value\"],\n\n  AssignmentPattern: [\"left\"],\n  ArrayPattern: [\"elements\"],\n  ObjectPattern: [\"properties\"],\n\n  VariableDeclaration: [\"declarations\"],\n  VariableDeclarator: [\"id\"],\n};\n\ngetBindingIdentifiers.keys = keys;\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAwCA,SAASC,qBAAqBA,CAC5BC,IAAY,EACZC,UAAoB,EACpBC,SAAmB,EACnBC,eAAyB,EAC2C;EACpE,MAAMC,MAAgB,GAAG,EAAE,CAACC,MAAM,CAACL,IAAI,CAAC;EACxC,MAAMM,GAAG,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAE/B,OAAOJ,MAAM,CAACK,MAAM,EAAE;IACpB,MAAMC,EAAE,GAAGN,MAAM,CAACO,KAAK,CAAC,CAAC;IACzB,IAAI,CAACD,EAAE,EAAE;IAET,IACEP,eAAe,KAMd,IAAAS,6BAAsB,EAACF,EAAE,CAAC,IACzB,IAAAG,wBAAiB,EAACH,EAAE,CAAC,IACrB,IAAAI,yBAAkB,EAACJ,EAAE,CAAC,CAAC,EACzB;MACA;IACF;IAEA,IAAI,IAAAK,mBAAY,EAACL,EAAE,CAAC,EAAE;MACpB,IAAIT,UAAU,EAAE;QACd,MAAMe,IAAI,GAAIV,GAAG,CAACI,EAAE,CAACO,IAAI,CAAC,GAAGX,GAAG,CAACI,EAAE,CAACO,IAAI,CAAC,IAAI,EAAG;QAChDD,IAAI,CAACE,IAAI,CAACR,EAAE,CAAC;MACf,CAAC,MAAM;QACLJ,GAAG,CAACI,EAAE,CAACO,IAAI,CAAC,GAAGP,EAAE;MACnB;MACA;IACF;IAEA,IAAI,IAAAS,0BAAmB,EAACT,EAAE,CAAC,IAAI,CAAC,IAAAU,6BAAsB,EAACV,EAAE,CAAC,EAAE;MAC1D,IAAI,IAAAW,oBAAa,EAACX,EAAE,CAACY,WAAW,CAAC,EAAE;QACjClB,MAAM,CAACc,IAAI,CAACR,EAAE,CAACY,WAAW,CAAC;MAC7B;MACA;IACF;IAEA,IAAIpB,SAAS,EAAE;MACb,IAAI,IAAAqB,4BAAqB,EAACb,EAAE,CAAC,EAAE;QAC7BN,MAAM,CAACc,IAAI,CAACR,EAAE,CAACA,EAAE,CAAC;QAClB;MACF;MAEA,IACE,IAAAc,2BAAoB,EAACd,EAAE,CAAC,EAExB;QACA;MACF;IACF;IAEA,MAAMe,IAAI,GAAG1B,qBAAqB,CAAC0B,IAAI,CAACf,EAAE,CAACgB,IAAI,CAAC;IAEhD,IAAID,IAAI,EAAE;MACR,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAAChB,MAAM,EAAEkB,CAAC,EAAE,EAAE;QACpC,MAAMC,GAAG,GAAGH,IAAI,CAACE,CAAC,CAAC;QACnB,MAAME,KAAK,GAETnB,EAAE,CAACkB,GAAG,CAAyC;QACjD,IAAIC,KAAK,EAAE;UACT,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;YACxBzB,MAAM,CAACc,IAAI,CAAC,GAAGW,KAAK,CAAC;UACvB,CAAC,MAAM;YACLzB,MAAM,CAACc,IAAI,CAACW,KAAK,CAAC;UACpB;QACF;MACF;IACF;EACF;EACA,OAAOvB,GAAG;AACZ;AASA,MAAMmB,IAAa,GAAG;EACpBO,YAAY,EAAE,CAAC,IAAI,CAAC;EACpBC,eAAe,EAAE,CAAC,IAAI,CAAC;EACvBC,aAAa,EAAE,CAAC,IAAI,CAAC;EACrBC,eAAe,EAAE,CAAC,IAAI,CAAC;EACvBC,gBAAgB,EAAE,CAAC,IAAI,CAAC;EACxBC,gBAAgB,EAAE,CAAC,IAAI,CAAC;EACxBC,iBAAiB,EAAE,CAAC,IAAI,CAAC;EACzBC,oBAAoB,EAAE,CAAC,IAAI,CAAC;EAC5BC,SAAS,EAAE,CAAC,IAAI,CAAC;EACjBC,UAAU,EAAE,CAAC,IAAI,CAAC;EAElBC,WAAW,EAAE,CAAC,OAAO,CAAC;EACtBC,gBAAgB,EAAE,CAAC,OAAO,CAAC;EAC3BC,eAAe,EAAE,CAAC,UAAU,CAAC;EAC7BC,oBAAoB,EAAE,CAAC,MAAM,CAAC;EAE9BC,eAAe,EAAE,CAAC,OAAO,CAAC;EAC1BC,wBAAwB,EAAE,CAAC,OAAO,CAAC;EACnCC,sBAAsB,EAAE,CAAC,OAAO,CAAC;EACjCC,iBAAiB,EAAE,CAAC,YAAY,CAAC;EACjCC,yBAAyB,EAAE,CAAC,IAAI,CAAC;EAEjCC,eAAe,EAAE,CAAC,UAAU,CAAC;EAC7BC,wBAAwB,EAAE,CAAC,UAAU,CAAC;EACtCC,sBAAsB,EAAE,CAAC,UAAU,CAAC;EAEpCC,mBAAmB,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;EACrCC,kBAAkB,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;EACpCC,uBAAuB,EAAE,CAAC,QAAQ,CAAC;EACnCC,YAAY,EAAE,CAAC,QAAQ,CAAC;EACxBC,WAAW,EAAE,CAAC,QAAQ,CAAC;EACvBC,kBAAkB,EAAE,CAAC,QAAQ,CAAC;EAE9BC,cAAc,EAAE,CAAC,MAAM,CAAC;EACxBC,cAAc,EAAE,CAAC,MAAM,CAAC;EAExBC,gBAAgB,EAAE,CAAC,IAAI,CAAC;EACxBC,eAAe,EAAE,CAAC,IAAI,CAAC;EAEvBC,WAAW,EAAE,CAAC,UAAU,CAAC;EACzBC,gBAAgB,EAAE,CAAC,UAAU,CAAC;EAE9BC,cAAc,EAAE,CAAC,OAAO,CAAC;EAEzBC,iBAAiB,EAAE,CAAC,MAAM,CAAC;EAC3BC,YAAY,EAAE,CAAC,UAAU,CAAC;EAC1BC,aAAa,EAAE,CAAC,YAAY,CAAC;EAE7BC,mBAAmB,EAAE,CAAC,cAAc,CAAC;EACrCC,kBAAkB,EAAE,CAAC,IAAI;AAC3B,CAAC;AAEDxE,qBAAqB,CAAC0B,IAAI,GAAGA,IAAI", "ignoreList": []}