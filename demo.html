<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Poker Player Manager</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .app {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .app-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .app-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .app-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .app-main {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .add-player-form {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 2px solid #f0f0f0;
        }

        .add-player-form h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .form-group {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .player-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .player-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-add {
            background: #667eea;
            color: white;
            min-width: 120px;
        }

        .btn-add:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-edit {
            background: #f39c12;
            color: white;
            padding: 8px 12px;
            font-size: 12px;
        }

        .btn-edit:hover {
            background: #e67e22;
        }

        .btn-remove {
            background: #e74c3c;
            color: white;
            padding: 8px 12px;
            font-size: 12px;
        }

        .btn-remove:hover {
            background: #c0392b;
        }

        .btn-save {
            background: #27ae60;
            color: white;
            padding: 6px 10px;
            font-size: 12px;
        }

        .btn-save:hover {
            background: #229954;
        }

        .btn-cancel {
            background: #95a5a6;
            color: white;
            padding: 6px 10px;
            font-size: 12px;
        }

        .btn-cancel:hover {
            background: #7f8c8d;
        }

        .player-list h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.5rem;
        }

        .player-list-empty {
            text-align: center;
            padding: 60px 20px;
            color: #666;
            font-style: italic;
        }

        .player-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }

        .player-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .player-item:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }

        .player-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .player-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            flex: 1;
        }

        .player-actions {
            display: flex;
            gap: 8px;
        }

        .player-edit {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .player-edit-input {
            padding: 10px 12px;
            border: 2px solid #667eea;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
        }

        .player-edit-input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .player-edit-buttons {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .app {
                padding: 15px;
            }
            
            .app-main {
                padding: 20px;
            }
            
            .form-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .player-grid {
                grid-template-columns: 1fr;
            }
            
            .app-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <header class="app-header">
            <h1>🃏 Poker Player Manager</h1>
            <p>Manage your poker night players</p>
        </header>
        
        <main class="app-main">
            <div class="add-player-form">
                <h2>Add New Player</h2>
                <form id="addPlayerForm">
                    <div class="form-group">
                        <input
                            type="text"
                            id="playerNameInput"
                            placeholder="Enter player name..."
                            class="player-input"
                            maxlength="50"
                        />
                        <button type="submit" class="btn btn-add">
                            Add Player
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="player-list">
                <h2 id="playerCount">Players (0)</h2>
                <div id="playerListEmpty" class="player-list-empty">
                    <p>No players added yet. Add your first player above!</p>
                </div>
                <div id="playerGrid" class="player-grid"></div>
            </div>
        </main>
    </div>

    <script>
        class PokerPlayerManager {
            constructor() {
                this.players = this.loadPlayers();
                this.nextId = this.getNextId();
                this.initializeEventListeners();
                this.render();
            }

            loadPlayers() {
                const saved = localStorage.getItem('pokerPlayers');
                return saved ? JSON.parse(saved) : [];
            }

            savePlayers() {
                localStorage.setItem('pokerPlayers', JSON.stringify(this.players));
            }

            getNextId() {
                return this.players.length > 0 ? Math.max(...this.players.map(p => p.id)) + 1 : 1;
            }

            initializeEventListeners() {
                const form = document.getElementById('addPlayerForm');
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.addPlayer();
                });
            }

            addPlayer() {
                const input = document.getElementById('playerNameInput');
                const name = input.value.trim();
                
                if (name && !this.players.some(p => p.name.toLowerCase() === name.toLowerCase())) {
                    this.players.push({
                        id: this.nextId++,
                        name: name
                    });
                    input.value = '';
                    this.savePlayers();
                    this.render();
                }
            }

            removePlayer(id) {
                this.players = this.players.filter(p => p.id !== id);
                this.savePlayers();
                this.render();
            }

            renamePlayer(id, newName) {
                const trimmedName = newName.trim();
                if (trimmedName && !this.players.some(p => p.id !== id && p.name.toLowerCase() === trimmedName.toLowerCase())) {
                    const player = this.players.find(p => p.id === id);
                    if (player) {
                        player.name = trimmedName;
                        this.savePlayers();
                        this.render();
                    }
                }
            }

            render() {
                const playerCount = document.getElementById('playerCount');
                const playerListEmpty = document.getElementById('playerListEmpty');
                const playerGrid = document.getElementById('playerGrid');

                playerCount.textContent = `Players (${this.players.length})`;

                if (this.players.length === 0) {
                    playerListEmpty.classList.remove('hidden');
                    playerGrid.innerHTML = '';
                } else {
                    playerListEmpty.classList.add('hidden');
                    playerGrid.innerHTML = this.players.map(player => this.renderPlayerItem(player)).join('');
                }
            }

            renderPlayerItem(player) {
                return `
                    <div class="player-item" data-player-id="${player.id}">
                        <div class="player-display">
                            <span class="player-name">${this.escapeHtml(player.name)}</span>
                            <div class="player-actions">
                                <button class="btn btn-edit" onclick="app.startEdit(${player.id})" title="Rename player">
                                    ✏️
                                </button>
                                <button class="btn btn-remove" onclick="app.removePlayer(${player.id})" title="Remove player">
                                    🗑️
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }

            startEdit(id) {
                const player = this.players.find(p => p.id === id);
                if (!player) return;

                const playerItem = document.querySelector(`[data-player-id="${id}"]`);
                playerItem.innerHTML = `
                    <div class="player-edit">
                        <input
                            type="text"
                            value="${this.escapeHtml(player.name)}"
                            class="player-edit-input"
                            id="edit-input-${id}"
                            maxlength="50"
                        />
                        <div class="player-edit-buttons">
                            <button class="btn btn-save" onclick="app.saveEdit(${id})">✓</button>
                            <button class="btn btn-cancel" onclick="app.cancelEdit(${id})">✗</button>
                        </div>
                    </div>
                `;

                const input = document.getElementById(`edit-input-${id}`);
                input.focus();
                input.select();

                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        this.saveEdit(id);
                    } else if (e.key === 'Escape') {
                        this.cancelEdit(id);
                    }
                });
            }

            saveEdit(id) {
                const input = document.getElementById(`edit-input-${id}`);
                const newName = input.value.trim();
                if (newName) {
                    this.renamePlayer(id, newName);
                } else {
                    this.render();
                }
            }

            cancelEdit(id) {
                this.render();
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // Initialize the app
        const app = new PokerPlayerManager();
    </script>
</body>
</html>
