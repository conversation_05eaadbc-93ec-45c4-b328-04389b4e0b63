import { useState, useEffect } from 'react'
import PlayerList from './components/PlayerList'
import AddPlayerForm from './components/AddPlayerForm'
import './App.css'

function App() {
  const [players, setPlayers] = useState([])

  // Load players from localStorage on component mount
  useEffect(() => {
    const savedPlayers = localStorage.getItem('pokerPlayers')
    if (savedPlayers) {
      setPlayers(JSON.parse(savedPlayers))
    }
  }, [])

  // Save players to localStorage whenever players array changes
  useEffect(() => {
    localStorage.setItem('pokerPlayers', JSON.stringify(players))
  }, [players])

  const addPlayer = (name) => {
    if (name.trim() && !players.some(player => player.name.toLowerCase() === name.toLowerCase())) {
      const newPlayer = {
        id: Date.now() + Math.random(), // Simple ID generation
        name: name.trim()
      }
      setPlayers([...players, newPlayer])
    }
  }

  const removePlayer = (id) => {
    setPlayers(players.filter(player => player.id !== id))
  }

  const renamePlayer = (id, newName) => {
    if (newName.trim() && !players.some(player => player.id !== id && player.name.toLowerCase() === newName.toLowerCase())) {
      setPlayers(players.map(player => 
        player.id === id ? { ...player, name: newName.trim() } : player
      ))
    }
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>🃏 Poker Player Manager</h1>
        <p>Manage your poker night players</p>
      </header>
      
      <main className="app-main">
        <AddPlayerForm onAddPlayer={addPlayer} />
        <PlayerList 
          players={players} 
          onRemovePlayer={removePlayer}
          onRenamePlayer={renamePlayer}
        />
      </main>
    </div>
  )
}

export default App
