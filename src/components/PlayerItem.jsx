import { useState } from 'react'

function PlayerItem({ player, onRemove, onRename }) {
  const [isEditing, setIsEditing] = useState(false)
  const [editName, setEditName] = useState(player.name)

  const handleSave = () => {
    if (editName.trim() && editName !== player.name) {
      onRename(editName)
    }
    setIsEditing(false)
    setEditName(player.name)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditName(player.name)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  return (
    <div className="player-item">
      {isEditing ? (
        <div className="player-edit">
          <input
            type="text"
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            onKeyDown={handleKeyPress}
            onBlur={handleSave}
            autoFocus
            className="player-edit-input"
          />
          <div className="player-edit-buttons">
            <button onClick={handleSave} className="btn btn-save">✓</button>
            <button onClick={handleCancel} className="btn btn-cancel">✗</button>
          </div>
        </div>
      ) : (
        <div className="player-display">
          <span className="player-name">{player.name}</span>
          <div className="player-actions">
            <button 
              onClick={() => setIsEditing(true)} 
              className="btn btn-edit"
              title="Rename player"
            >
              ✏️
            </button>
            <button 
              onClick={onRemove} 
              className="btn btn-remove"
              title="Remove player"
            >
              🗑️
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default PlayerItem
