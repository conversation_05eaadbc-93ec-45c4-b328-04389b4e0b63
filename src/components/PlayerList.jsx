import { useState } from 'react'
import PlayerItem from './PlayerItem'

function PlayerList({ players, onRemovePlayer, onRenamePlayer }) {
  if (players.length === 0) {
    return (
      <div className="player-list-empty">
        <p>No players added yet. Add your first player above!</p>
      </div>
    )
  }

  return (
    <div className="player-list">
      <h2>Players ({players.length})</h2>
      <div className="player-grid">
        {players.map(player => (
          <PlayerItem
            key={player.id}
            player={player}
            onRemove={() => onRemovePlayer(player.id)}
            onRename={(newName) => onRenamePlayer(player.id, newName)}
          />
        ))}
      </div>
    </div>
  )
}

export default PlayerList
